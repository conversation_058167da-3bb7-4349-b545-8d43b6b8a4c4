# Build stage
FROM oven/bun:1.1.42 AS builder

WORKDIR /app

# Accept build arguments for environment variables
ARG VITE_API_BASE_URL
ARG VITE_GOLD_API_URL
ARG VITE_APP_NAME

# Set environment variables for build
ENV VITE_API_BASE_URL=${VITE_API_BASE_URL}
ENV VITE_GOLD_API_URL=${VITE_GOLD_API_URL}
ENV VITE_APP_NAME=${VITE_APP_NAME}

# Copy root package.json and workspace files
COPY package.json bun.lock tsconfig.json ./
COPY shared/package.json ./shared/
COPY server/package.json ./server/
COPY client/package.json ./client/

# Install dependencies (skip postinstall scripts)
RUN bun install --ignore-scripts

# Copy shared workspace and build it
COPY shared ./shared
RUN cd shared && bun run build

# Skip server build for now - client doesn't need it for static build

# Copy client source code
COPY client ./client

# Validate required environment variables before build
RUN if [ -z "$VITE_API_BASE_URL" ]; then echo "Error: VITE_API_BASE_URL is required for build" && exit 1; fi
RUN if [ -z "$VITE_GOLD_API_URL" ]; then echo "Error: VITE_GOLD_API_URL is required for build" && exit 1; fi
RUN if [ -z "$VITE_APP_NAME" ]; then echo "Error: VITE_APP_NAME is required for build" && exit 1; fi

# Build the client app
RUN cd client && bun run build

# Production stage
FROM nginx:alpine AS production

# Copy built files to nginx
COPY --from=builder /app/client/dist /usr/share/nginx/html

# Copy the proven working nginx config
COPY client/nginx.conf /etc/nginx/nginx.conf

# Remove default config to avoid conflicts
RUN rm -f /etc/nginx/conf.d/default.conf

# Expose port 80 (as per working config)
EXPOSE 80

# Start nginx (as root user to bind to port 80)
CMD ["nginx", "-g", "daemon off;"]
