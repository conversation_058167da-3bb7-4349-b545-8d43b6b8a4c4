# Build stage
FROM oven/bun:1.1.42 AS builder

WORKDIR /app

# No build-time environment variables needed for runtime config

# Copy root package.json and workspace files
COPY package.json bun.lock tsconfig.json ./
COPY shared/package.json ./shared/
COPY server/package.json ./server/
COPY client/package.json ./client/

# Install dependencies (skip postinstall scripts)
RUN bun install --ignore-scripts

# Copy shared workspace and build it
COPY shared ./shared
RUN cd shared && bun run build

# Skip server build for now - client doesn't need it for static build

# Copy client source code
COPY client ./client

# Build the client app (no environment variables needed at build time)
RUN cd client && bun run build

# Production stage
FROM nginx:alpine AS production

# Install envsubst for runtime environment variable substitution
RUN apk add --no-cache gettext

# Copy built files to nginx
COPY --from=builder /app/client/dist /usr/share/nginx/html

# Copy environment template
COPY client/public/env.template.js /usr/share/nginx/html/env.template.js

# Copy the proven working nginx config
COPY client/nginx.conf /etc/nginx/nginx.conf

# Remove default config to avoid conflicts
RUN rm -f /etc/nginx/conf.d/default.conf

# Expose port 80 (as per working config)
EXPOSE 80

# Create startup script that generates env.js from template and starts nginx
RUN echo '#!/bin/sh' > /start.sh && \
    echo 'echo "=== SUMOPOD CLIENT STARTING ==="' >> /start.sh && \
    echo 'echo "Generating env.js from environment variables..."' >> /start.sh && \
    echo 'envsubst < /usr/share/nginx/html/env.template.js > /usr/share/nginx/html/env.js' >> /start.sh && \
    echo 'echo "Generated env.js:"' >> /start.sh && \
    echo 'cat /usr/share/nginx/html/env.js' >> /start.sh && \
    echo 'echo "Starting nginx..."' >> /start.sh && \
    echo 'exec nginx -g "daemon off;"' >> /start.sh && \
    chmod +x /start.sh

# Use CMD instead of ENTRYPOINT to override nginx default
CMD ["/start.sh"]
