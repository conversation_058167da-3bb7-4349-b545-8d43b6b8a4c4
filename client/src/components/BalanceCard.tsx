import { useEffect, useState } from 'react';
import { getSessionToken, makeAuthenticatedRequest } from '../utils/sessionManager';

function BalanceCard() {
  const [balance, setBalance] = useState<number | null>(null);

  useEffect(() => {
    if (!getSessionToken()) return;

    makeAuthenticatedRequest('/api/data/balance', {}, 'BalanceCard')
      .then((res) => {
        if (res) {
          setBalance(res.userBalance ?? 0);
        }
      })
      .catch(() => {
        setBalance(0);
      });
  }, []);

  const formatCurrency = (amount: number): string =>
    new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
    }).format(amount);

  if (balance === null) return null;

  return (
    <div className="bg-white p-6 rounded-lg shadow-lg mt-6">
      <h2 className="text-sm font-bold mb-1">Current Credits</h2>
      <p className="text-2xl font-bold text-blue-600">
        {formatCurrency(balance)}
      </p>
    </div>
  );
}

export default BalanceCard;
