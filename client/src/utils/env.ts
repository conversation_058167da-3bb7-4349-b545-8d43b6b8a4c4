/**
 * Environment variable validation utility
 * Ensures required environment variables are present at runtime
 */

interface EnvConfig {
  VITE_API_BASE_URL: string;
  VITE_GOLD_API_URL: string;
  VITE_APP_NAME: string;
}

/**
 * Validates and returns environment variables
 * Throws an error if required variables are missing
 */
export function validateEnv(): EnvConfig {
  const requiredVars = {
    VITE_API_BASE_URL: import.meta.env.VITE_API_BASE_URL,
    VITE_GOLD_API_URL: import.meta.env.VITE_GOLD_API_URL,
    VITE_APP_NAME: import.meta.env.VITE_APP_NAME,
  };

  const missingVars: string[] = [];

  // Check for missing required variables
  Object.entries(requiredVars).forEach(([key, value]) => {
    if (!value || value.trim() === '') {
      missingVars.push(key);
    }
  });

  if (missingVars.length > 0) {
    const errorMessage = `Missing required environment variables: ${missingVars.join(', ')}. Please check your .env file.`;
    console.error(errorMessage);
    throw new Error(errorMessage);
  }

  return requiredVars as EnvConfig;
}

/**
 * Get validated environment configuration
 * Use this instead of directly accessing import.meta.env
 */
export const env = validateEnv();

/**
 * Safe API base URL getter with validation
 */
export function getApiBaseUrl(): string {
  const apiUrl = import.meta.env.VITE_API_BASE_URL;
  if (!apiUrl) {
    throw new Error('VITE_API_BASE_URL is not configured. Please check your .env file.');
  }
  return apiUrl;
}

/**
 * Safe Gold API URL getter with validation
 */
export function getGoldApiUrl(): string {
  const goldApiUrl = import.meta.env.VITE_GOLD_API_URL;
  if (!goldApiUrl) {
    throw new Error('VITE_GOLD_API_URL is not configured. Please check your .env file.');
  }
  return goldApiUrl;
}
