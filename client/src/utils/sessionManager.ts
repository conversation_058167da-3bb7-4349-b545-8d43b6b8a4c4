// Centralized session management to prevent race conditions
let isRedirecting = false;
let redirectTimeout: NodeJS.Timeout | null = null;

export const getSessionToken = (): string | null => {
  return (
    localStorage.getItem('session_token') ||
    sessionStorage.getItem('session_token')
  );
};

export const clearSessionAndRedirect = (reason: string = 'Session expired') => {
  // Prevent multiple simultaneous redirects
  if (isRedirecting) {
    console.log('Redirect already in progress, skipping...');
    return;
  }

  console.log(`Clearing session: ${reason}`);
  isRedirecting = true;

  // Clear session data
  localStorage.removeItem('session_token');
  localStorage.removeItem('user_email');
  localStorage.removeItem('user_id');
  sessionStorage.removeItem('session_token');

  // Debounce redirect to prevent multiple calls
  if (redirectTimeout) {
    clearTimeout(redirectTimeout);
  }

  redirectTimeout = setTimeout(() => {
    window.location.href = '/login';
  }, 100);
};

export const makeAuthenticatedRequest = async (
  endpoint: string,
  options: RequestInit = {},
  componentName: string = 'Unknown'
): Promise<any> => {
  const token = getSessionToken();
  
  if (!token) {
    console.log(`${componentName} - No session token found`);
    clearSessionAndRedirect('No session token');
    return null;
  }

  // Get API base URL from config
  const apiBaseUrl = window._env_?.API_URL;

  const requestConfig: RequestInit = {
    headers: {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${token}`,
      ...options.headers,
    },
    ...options,
  };

  const fullUrl = `${apiBaseUrl}${endpoint}`;

  try {
    const response = await fetch(fullUrl, requestConfig);

    if (response.status === 401) {
      console.log(`${componentName} - Received 401 Unauthorized`);
      clearSessionAndRedirect('401 Unauthorized');
      return null;
    }

    if (!response.ok) {
      console.error(`${componentName} - API call failed:`, response.status, response.statusText);
      return null;
    }

    return await response.json();
  } catch (error) {
    console.error(`${componentName} - Network error:`, error);
    return null;
  }
};

// Reset redirect flag when user successfully navigates
export const resetRedirectFlag = () => {
  isRedirecting = false;
  if (redirectTimeout) {
    clearTimeout(redirectTimeout);
    redirectTimeout = null;
  }
};
