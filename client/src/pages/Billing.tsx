import { useState } from 'react';
import BalanceCard from '../components/BalanceCard';
import TopUpModal from '../components/TopUpModal';
import TransactionTable from '../components/TransactionTable';

function Billing() {
  const [modalOpen, setModalOpen] = useState(false);

  const handleTopUp = (_amount: number) => {};

  return (
    <div className="p-4 sm:p-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold">Billing</h1>
          <p className="text-gray-600">
            Manage your balance and view transaction history
          </p>
        </div>
        <div className="flex gap-4">
          <button
            type="button"
            className="bg-gray-300 text-black px-4 py-2 rounded-lg"
          >
            Redeem
          </button>
          <button
            type="button"
            className="bg-blue-500 text-white px-4 py-2 rounded-lg"
            onClick={() => setModalOpen(true)}
          >
            Add Credit
          </button>
        </div>
      </div>

      <TopUpModal
        isOpen={modalOpen}
        onClose={() => setModalOpen(false)}
        onTopUp={handleTopUp}
      />

      <div className="mt-6">
        <BalanceCard />
      </div>

      <div className="mt-6">
        <TransactionTable />
      </div>
    </div>
  );
}

export default Billing;
