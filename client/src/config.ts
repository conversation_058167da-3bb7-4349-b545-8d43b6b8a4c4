// Configuration values for the application
// These values are hardcoded to ensure they're available in production

export const config = {
  // API base URL for backend services
  apiBaseUrl: 'https://wcwtnxxyerii.ap-southeast-1.clawcloudrun.com',
  
  // External API URLs
  goldApiUrl: 'https://logam-mulia-api.vercel.app/prices/anekalogam',
  
  // Application configuration
  appName: 'Sumopod',
};

// Helper functions to access configuration values
export function getApiBaseUrl(): string {
  // Try to get from environment variables first (for development)
  const envApiUrl = import.meta.env.VITE_API_BASE_URL;
  
  // Fall back to hardcoded value if environment variable is not available
  return envApiUrl || config.apiBaseUrl;
}

export function getGoldApiUrl(): string {
  const envGoldApiUrl = import.meta.env.VITE_GOLD_API_URL;
  return envGoldApiUrl || config.goldApiUrl;
}

export function getAppName(): string {
  const envAppName = import.meta.env.VITE_APP_NAME;
  return envAppName || config.appName;
}
