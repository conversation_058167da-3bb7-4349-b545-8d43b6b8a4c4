// Configuration values for the application
// These values are hardcoded to ensure they're available in production

export const config = {
  // API base URLs for different deployment platforms
  apiBaseUrls: {
    clawcloud: 'https://wcwtnxxyerii.ap-southeast-1.clawcloudrun.com',
    easypanel: 'https://sumopod-backend.rl5j77.easypanel.host',
    development: 'http://localhost:8080',
  },

  // External API URLs
  goldApiUrl: 'https://logam-mulia-api.vercel.app/prices/anekalogam',

  // Application configuration
  appName: 'Sumopod',
};

// Helper functions to access configuration values
export function getApiBaseUrl(): string {
  // Try to get from environment variables first
  const envApiUrl = import.meta.env.VITE_API_BASE_URL;
  if (envApiUrl) {
    return envApiUrl;
  }

  // Auto-detect based on current hostname
  const hostname = window.location.hostname;

  if (hostname.includes('easypanel.host')) {
    return config.apiBaseUrls.easypanel;
  } else if (hostname.includes('clawcloudrun.com')) {
    return config.apiBaseUrls.clawcloud;
  } else if (hostname === 'localhost' || hostname === '127.0.0.1') {
    return config.apiBaseUrls.development;
  }

  // Default fallback to Easy Panel (current primary deployment)
  return config.apiBaseUrls.easypanel;
}

export function getGoldApiUrl(): string {
  const envGoldApiUrl = import.meta.env.VITE_GOLD_API_URL;
  return envGoldApiUrl || config.goldApiUrl;
}

export function getAppName(): string {
  const envAppName = import.meta.env.VITE_APP_NAME;
  return envAppName || config.appName;
}
