// Runtime configuration that reads from window._env_
// This allows environment variables to be set at runtime, not build time

declare global {
  interface Window {
    _env_?: {
      API_URL?: string;
      APP_NAME?: string;
      GOLD_API?: string;
    };
  }
}

// Configuration with runtime environment variables only
const config = {
  // API base URL - reads from runtime env only
  get apiBaseUrl(): string {
    const apiUrl = window._env_?.API_URL;
    if (!apiUrl) {
      throw new Error('API_URL environment variable is required but not set');
    }
    return apiUrl;
  },

  // Gold API URL - reads from runtime env only
  get goldApiUrl(): string {
    const goldApi = window._env_?.GOLD_API;
    if (!goldApi) {
      throw new Error('GOLD_API environment variable is required but not set');
    }
    return goldApi;
  },

  // Application name - reads from runtime env only
  get appName(): string {
    const appName = window._env_?.APP_NAME;
    if (!appName) {
      throw new Error('APP_NAME environment variable is required but not set');
    }
    return appName;
  },
};

// Helper functions for accessing configuration values
export function getApiBaseUrl(): string {
  return config.apiBaseUrl;
}

export function getGoldApiUrl(): string {
  return config.goldApiUrl;
}

export function getAppName(): string {
  return config.appName;
}

export default config;
