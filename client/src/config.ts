// Runtime configuration that reads from window._env_
// This allows environment variables to be set at runtime, not build time

declare global {
  interface Window {
    _env_?: {
      API_URL?: string;
      APP_NAME?: string;
      GOLD_API?: string;
    };
  }
}

// Configuration with runtime environment variables and fallbacks
const config = {
  // API base URL - reads from runtime env or falls back to Easy Panel URL
  get apiBaseUrl(): string {
    return window._env_?.API_URL || 'https://sumopod-backend.rl5j77.easypanel.host';
  },
  
  // Gold API URL - reads from runtime env or falls back to default
  get goldApiUrl(): string {
    return window._env_?.GOLD_API || 'https://logam-mulia-api.vercel.app/prices/anekalogam';
  },
  
  // Application name - reads from runtime env or falls back to default
  get appName(): string {
    return window._env_?.APP_NAME || 'Sumopod';
  },
};

// Helper functions for accessing configuration values
export function getApiBaseUrl(): string {
  return config.apiBaseUrl;
}

export function getGoldApiUrl(): string {
  return config.goldApiUrl;
}

export function getAppName(): string {
  return config.appName;
}

export default config;
