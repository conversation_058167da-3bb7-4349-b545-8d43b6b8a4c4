# Production Environment Variables Template
# Copy this file to .env.production and update with your actual production values
# NEVER commit this file with real values to version control

# Database Configuration
DATABASE_URL=postgresql://username:password@host:port/database?sslmode=require

# Authentication Configuration
BETTER_AUTH_SECRET=your-production-secret-key-here
BETTER_AUTH_URL=https://your-backend-domain.com
BETTER_AUTH_TRUSTED_ORIGINS=https://your-frontend-domain.com

# Xendit Payment Gateway Configuration
XENDIT_API_KEY=your-production-xendit-api-key
XENDIT_API_URL=https://api.xendit.co/v2/invoices
XENDIT_CALLBACK_TOKEN=your-production-xendit-callback-token

# CORS Configuration
CORS_ORIGINS=https://your-frontend-domain.com
CORS_ALLOW_HEADERS=Content-Type,Authorization,X-Session-Token
CORS_ALLOW_METHODS=GET,POST,PUT,DELETE,OPTIONS

# Application Configuration
APP_NAME=sumopod-backend
EXTERNAL_ID_PREFIX=sumopod-

# Client Environment Variables (for Docker build)
VITE_API_BASE_URL=https://your-backend-domain.com
VITE_GOLD_API_URL=https://logam-mulia-api.vercel.app/prices/anekalogam
VITE_APP_NAME=Sumopod
