version: '3.8'

services:
  sumopod-server:
    image: hashiif/sumopod-server:v1.3
    container_name: sumopod-server
    ports:
      - "8080:8080"
    environment:
      # Database Configuration
      - DATABASE_URL=${DATABASE_URL}
      
      # Authentication Configuration
      - BETTER_AUTH_SECRET=${BETTER_AUTH_SECRET}
      - BETTER_AUTH_URL=${BETTER_AUTH_URL}
      - BETTER_AUTH_TRUSTED_ORIGINS=${BETTER_AUTH_TRUSTED_ORIGINS}
      
      # Xendit Payment Configuration
      - XENDIT_API_KEY=${XENDIT_API_KEY}
      - XENDIT_API_URL=${XENDIT_API_URL}
      - XENDIT_CALLBACK_TOKEN=${XENDIT_CALLBACK_TOKEN}
      
      # Server Configuration
      - PORT=8080
      
      # CORS Configuration
      - CORS_ORIGINS=${CORS_ORIGINS}
      - CORS_ALLOW_HEADERS=${CORS_ALLOW_HEADERS}
      - CORS_ALLOW_METHODS=${CORS_ALLOW_METHODS}
      
      # Application Configuration
      - APP_NAME=${APP_NAME}
      - EXTERNAL_ID_PREFIX=${EXTERNAL_ID_PREFIX}
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  sumopod-client:
    image: hashiif/sumopod-client:v1.11
    container_name: sumopod-client
    ports:
      - "3000:3000"
    environment:
      - VITE_API_URL=https://wcwtnxxyerii.ap-southeast-1.clawcloudrun.com
    restart: unless-stopped
    depends_on:
      - sumopod-server
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

networks:
  default:
    name: sumopod-network
