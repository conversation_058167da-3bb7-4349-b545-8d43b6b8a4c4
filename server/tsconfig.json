{"compilerOptions": {"outDir": "./dist", "rootDir": "./src", "noEmit": false, "declaration": true, "declarationMap": true, "sourceMap": true, "target": "ES2022", "module": "ESNext", "moduleResolution": "node", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "strict": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "isolatedModules": true, "noUnusedLocals": false, "noUnusedParameters": false, "lib": ["ESNext"], "types": ["node", "bun-types"]}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.spec.ts"]}